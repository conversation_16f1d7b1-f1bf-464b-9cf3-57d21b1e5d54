<?php
// Test file to check P<PERSON> and settings
echo "<h1>System Test</h1>";
echo "<p>PHP is working correctly!</p>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Current Time: " . date('Y-m-d H:i:s') . "</p>";

// Test Telegram connection
require_once 'telegram_config.php';

echo "<h2>Telegram Settings Test:</h2>";
echo "<p>Bot Token: " . (defined('TELEGRAM_BOT_TOKEN') ? 'Defined ✓' : 'Not Defined ✗') . "</p>";
echo "<p>Chat ID: " . (defined('TELEGRAM_CHAT_ID') ? 'Defined ✓' : 'Not Defined ✗') . "</p>";

// Test sending message
if (isset($_GET['test_telegram'])) {
    $test_message = "🧪 Test message from system\n⏰ " . date('Y-m-d H:i:s');
    $result = sendToTelegram($test_message);

    if ($result && isset($result['ok']) && $result['ok']) {
        echo "<p style='color: green;'>✅ Test message sent successfully!</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to send test message</p>";
        echo "<pre>" . print_r($result, true) . "</pre>";
    }
}

echo "<h2>Links:</h2>";
echo "<ul>";
echo "<li><a href='stand.php'>Homepage</a></li>";
echo "<li><a href='admin_panel.php'>Admin Panel</a></li>";
echo "<li><a href='test.php?test_telegram=1'>Test Telegram</a></li>";
echo "</ul>";

echo "<h2>Server Information:</h2>";
echo "<p>Operating System: " . PHP_OS . "</p>";
echo "<p>Web Server: " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p>Current Directory: " . __DIR__ . "</p>";

// Check required files
$required_files = ['stand.php', 'process_payment.php', 'telegram_config.php', 'admin_panel.php'];
echo "<h2>Required Files:</h2>";
echo "<ul>";
foreach ($required_files as $file) {
    $exists = file_exists($file);
    $status = $exists ? '✅' : '❌';
    echo "<li>{$status} {$file}</li>";
}
echo "</ul>";
?>
