<?php
// ملف اختبار للتحقق من عمل PHP والإعدادات
echo "<h1>اختبار النظام</h1>";
echo "<p>PHP يعمل بشكل صحيح!</p>";
echo "<p>إصدار PHP: " . phpversion() . "</p>";
echo "<p>الوقت الحالي: " . date('Y-m-d H:i:s') . "</p>";

// اختبار الاتصال بالتليجرام
require_once 'telegram_config.php';

echo "<h2>اختبار إعدادات التليجرام:</h2>";
echo "<p>توكن البوت: " . (defined('TELEGRAM_BOT_TOKEN') ? 'محدد ✓' : 'غير محدد ✗') . "</p>";
echo "<p>معرف المحادثة: " . (defined('TELEGRAM_CHAT_ID') ? 'محدد ✓' : 'غير محدد ✗') . "</p>";

// اختبار إرسال رسالة
if (isset($_GET['test_telegram'])) {
    $test_message = "🧪 رسالة اختبار من النظام\n⏰ " . date('Y-m-d H:i:s');
    $result = sendToTelegram($test_message);
    
    if ($result && isset($result['ok']) && $result['ok']) {
        echo "<p style='color: green;'>✅ تم إرسال رسالة الاختبار بنجاح!</p>";
    } else {
        echo "<p style='color: red;'>❌ فشل في إرسال رسالة الاختبار</p>";
        echo "<pre>" . print_r($result, true) . "</pre>";
    }
}

echo "<h2>الروابط:</h2>";
echo "<ul>";
echo "<li><a href='stand.php'>الصفحة الرئيسية</a></li>";
echo "<li><a href='admin_panel.php'>لوحة الإدارة</a></li>";
echo "<li><a href='test.php?test_telegram=1'>اختبار التليجرام</a></li>";
echo "</ul>";

echo "<h2>معلومات الخادم:</h2>";
echo "<p>نظام التشغيل: " . PHP_OS . "</p>";
echo "<p>خادم الويب: " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p>المجلد الحالي: " . __DIR__ . "</p>";

// التحقق من الملفات المطلوبة
$required_files = ['stand.php', 'process_payment.php', 'telegram_config.php', 'admin_panel.php'];
echo "<h2>الملفات المطلوبة:</h2>";
echo "<ul>";
foreach ($required_files as $file) {
    $exists = file_exists($file);
    $status = $exists ? '✅' : '❌';
    echo "<li>{$status} {$file}</li>";
}
echo "</ul>";
?>
