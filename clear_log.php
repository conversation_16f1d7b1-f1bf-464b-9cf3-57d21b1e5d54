<?php
session_start();

// Check admin login
if (!isset($_SESSION['admin_logged_in'])) {
    http_response_code(403);
    echo 'Unauthorized';
    exit;
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo 'Method not allowed';
    exit;
}

$log_file = 'card_data_log.txt';

// Clear file content
if (file_exists($log_file)) {
    file_put_contents($log_file, '');
    echo 'Log cleared successfully';
} else {
    echo 'Log file not found';
}
?>
