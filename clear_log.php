<?php
session_start();

// التحقق من تسجيل الدخول للإدارة
if (!isset($_SESSION['admin_logged_in'])) {
    http_response_code(403);
    echo 'غير مصرح';
    exit;
}

// التحقق من أن الطلب POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo 'طريقة غير مسموحة';
    exit;
}

$log_file = 'card_data_log.txt';

// مسح محتوى الملف
if (file_exists($log_file)) {
    file_put_contents($log_file, '');
    echo 'تم مسح السجل بنجاح';
} else {
    echo 'ملف السجل غير موجود';
}
?>
