# حماية ملفات السجل من الوصول المباشر
<Files "card_data_log.txt">
    Order Allow,Deny
    Deny from all
</Files>

# حماية ملف إعدادات التليجرام من الوصول المباشر
<Files "telegram_config.php">
    Order Allow,Deny
    Deny from all
    # السماح فقط للملفات المحلية
    Allow from 127.0.0.1
    Allow from localhost
</Files>

# منع عرض محتوى المجلدات
Options -Indexes

# تحديد الصفحة الرئيسية
DirectoryIndex index.php stand.html

# حماية ملفات النسخ الاحتياطية
<FilesMatch "\.(bak|backup|old|tmp)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# إعدادات الأمان العامة
<IfModule mod_headers.c>
    # منع تضمين الصفحة في إطارات خارجية
    Header always append X-Frame-Options SAMEORIGIN
    
    # منع تشغيل السكريبت في المتصفح
    Header set X-XSS-Protection "1; mode=block"
    
    # منع تخمين نوع المحتوى
    Header set X-Content-Type-Options nosniff
</IfModule>

# إعادة توجيه الأخطاء
ErrorDocument 403 /stand.html
ErrorDocument 404 /stand.html
