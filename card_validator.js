/**
 * Advanced Credit Card Validation Library
 * Includes Luhn Algorithm and comprehensive card type detection
 */

class CardValidator {
    constructor() {
        this.cardTypes = {
            visa: {
                pattern: /^4/,
                lengths: [13, 16, 19],
                cvvLength: 3,
                icon: 'fab fa-cc-visa',
                color: '#1a1f71',
                name: 'Visa'
            },
            mastercard: {
                pattern: /^5[1-5]|^2[2-7]/,
                lengths: [16],
                cvvLength: 3,
                icon: 'fab fa-cc-mastercard',
                color: '#eb001b',
                name: 'Mastercard'
            },
            amex: {
                pattern: /^3[47]/,
                lengths: [15],
                cvvLength: 4,
                icon: 'fab fa-cc-amex',
                color: '#006fcf',
                name: 'American Express'
            },
            discover: {
                pattern: /^6(?:011|5|4[4-9]|22[1-9])/,
                lengths: [16],
                cvvLength: 3,
                icon: 'fab fa-cc-discover',
                color: '#ff6000',
                name: 'Discover'
            },
            jcb: {
                pattern: /^35/,
                lengths: [16],
                cvvLength: 3,
                icon: 'fab fa-cc-jcb',
                color: '#0e4c96',
                name: '<PERSON><PERSON><PERSON>'
            },
            dinersclub: {
                pattern: /^3[068]/,
                lengths: [14],
                cvvLength: 3,
                icon: 'fab fa-cc-diners-club',
                color: '#0079be',
                name: 'Diners Club'
            }
        };
    }

    /**
     * Luhn Algorithm Implementation
     * @param {string} cardNumber - Card number to validate
     * @returns {boolean} - True if valid according to Luhn algorithm
     */
    luhnCheck(cardNumber) {
        const digits = cardNumber.replace(/\D/g, '');
        let sum = 0;
        let isEven = false;
        
        // Process digits from right to left
        for (let i = digits.length - 1; i >= 0; i--) {
            let digit = parseInt(digits[i]);
            
            if (isEven) {
                digit *= 2;
                if (digit > 9) {
                    digit -= 9;
                }
            }
            
            sum += digit;
            isEven = !isEven;
        }
        
        return sum % 10 === 0;
    }

    /**
     * Detect card type based on number pattern
     * @param {string} cardNumber - Card number to analyze
     * @returns {object} - Card type information
     */
    detectCardType(cardNumber) {
        const number = cardNumber.replace(/\D/g, '');
        
        for (const [type, config] of Object.entries(this.cardTypes)) {
            if (config.pattern.test(number)) {
                return { type, ...config };
            }
        }
        
        return {
            type: 'unknown',
            pattern: null,
            lengths: [13, 14, 15, 16, 17, 18, 19],
            cvvLength: 3,
            icon: 'far fa-credit-card',
            color: '#6b7280',
            name: 'Unknown'
        };
    }

    /**
     * Comprehensive card validation
     * @param {string} cardNumber - Card number to validate
     * @returns {object} - Validation result
     */
    validateCard(cardNumber) {
        const number = cardNumber.replace(/\D/g, '');
        const cardType = this.detectCardType(number);
        
        // Check if empty
        if (number.length === 0) {
            return { 
                valid: null, 
                message: '', 
                cardType: cardType.type 
            };
        }
        
        // Check minimum length
        if (number.length < 13) {
            return { 
                valid: false, 
                message: 'Card number too short', 
                cardType: cardType.type 
            };
        }
        
        // Check maximum length
        if (number.length > 19) {
            return { 
                valid: false, 
                message: 'Card number too long', 
                cardType: cardType.type 
            };
        }
        
        // Check if length matches card type
        if (!cardType.lengths.includes(number.length)) {
            return { 
                valid: false, 
                message: `Invalid length for ${cardType.name}`, 
                cardType: cardType.type 
            };
        }
        
        // Check Luhn algorithm
        if (!this.luhnCheck(number)) {
            return {
                valid: false,
                message: 'Invalid card number',
                cardType: cardType.type
            };
        }
        
        return { 
            valid: true, 
            message: `Valid ${cardType.name} card`, 
            cardType: cardType.type 
        };
    }

    /**
     * Validate expiry date
     * @param {string} expiryDate - Expiry date in MM/YY format
     * @returns {object} - Validation result
     */
    validateExpiryDate(expiryDate) {
        if (!expiryDate || expiryDate.length !== 5 || !expiryDate.includes('/')) {
            return { valid: false, message: 'Invalid format (use MM/YY)' };
        }
        
        const [month, year] = expiryDate.split('/');
        const currentDate = new Date();
        const currentYear = currentDate.getFullYear() % 100;
        const currentMonth = currentDate.getMonth() + 1;
        
        const monthNum = parseInt(month);
        const yearNum = parseInt(year);
        
        // Validate month
        if (monthNum < 1 || monthNum > 12) {
            return { valid: false, message: 'Invalid month (01-12)' };
        }
        
        // Check if card is expired
        if (yearNum < currentYear || (yearNum === currentYear && monthNum < currentMonth)) {
            return { valid: false, message: 'Card has expired' };
        }
        
        // Check if expiry is too far in future (more than 10 years)
        if (yearNum > currentYear + 10) {
            return { valid: false, message: 'Invalid expiry year' };
        }
        
        return { valid: true, message: 'Valid expiry date' };
    }

    /**
     * Validate CVV
     * @param {string} cvv - CVV code
     * @param {string} cardType - Card type
     * @returns {object} - Validation result
     */
    validateCVV(cvv, cardType = 'unknown') {
        if (!cvv || !/^\d+$/.test(cvv)) {
            return { valid: false, message: 'CVV must contain only numbers' };
        }
        
        const cardConfig = this.cardTypes[cardType] || { cvvLength: 3 };
        const expectedLength = cardConfig.cvvLength;
        
        if (cvv.length !== expectedLength) {
            return { 
                valid: false, 
                message: `CVV must be ${expectedLength} digits for ${cardConfig.name || 'this card type'}` 
            };
        }
        
        return { valid: true, message: 'Valid CVV' };
    }

    /**
     * Validate cardholder name
     * @param {string} name - Cardholder name
     * @returns {object} - Validation result
     */
    validateName(name) {
        if (!name || name.trim().length < 2) {
            return { valid: false, message: 'Name must be at least 2 characters' };
        }
        
        if (name.trim().length > 50) {
            return { valid: false, message: 'Name is too long' };
        }
        
        // Check for valid characters (letters, spaces, hyphens, apostrophes)
        if (!/^[a-zA-Z\s\-'\.]+$/.test(name)) {
            return { valid: false, message: 'Name contains invalid characters' };
        }
        
        return { valid: true, message: 'Valid name' };
    }

    /**
     * Format card number with spaces
     * @param {string} cardNumber - Raw card number
     * @returns {string} - Formatted card number
     */
    formatCardNumber(cardNumber) {
        const number = cardNumber.replace(/\D/g, '');
        const cardType = this.detectCardType(number);
        
        // American Express uses 4-6-5 format
        if (cardType.type === 'amex') {
            return number.replace(/(\d{4})(\d{6})(\d{5})/, '$1 $2 $3');
        }
        
        // Most other cards use 4-4-4-4 format
        return number.replace(/(\d{4})/g, '$1 ').trim();
    }

    /**
     * Generate test card numbers for different types (for testing only)
     * @param {string} type - Card type
     * @returns {string} - Test card number
     */
    generateTestCard(type = 'visa') {
        const testCards = {
            visa: '****************',
            mastercard: '****************',
            amex: '***************',
            discover: '****************'
        };
        
        return testCards[type] || testCards.visa;
    }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CardValidator;
}

// Make available globally in browser
if (typeof window !== 'undefined') {
    window.CardValidator = CardValidator;
}
